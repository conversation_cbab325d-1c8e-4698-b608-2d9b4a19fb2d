export default class ModuleContainer {
  constructor({ 
    id, 
    name, 
    description, 
    thumbnail, 
    modules = [], 
    totalModules = 0,
    totalSteps = 0,
    totalQuizQuestions = 0,
    createdAt, 
    updatedAt, 
    tags = [],
    isContainer = true,
    isLocked = false,
    isComingSoon = false
  }) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.thumbnail = thumbnail;
    this.modules = modules; // Array of sub-modules
    this.totalModules = totalModules;
    this.totalSteps = totalSteps;
    this.totalQuizQuestions = totalQuizQuestions;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.tags = tags;
    this.isContainer = isContainer;
    this.isLocked = isLocked;
    this.isComingSoon = isComingSoon;
  }

  // Helper methods
  getCompletedModulesCount(userProgress = {}) {
    return this.modules.filter(module =>
      !module.isLocked && userProgress[module.id]?.completed
    ).length;
  }

  getOverallProgress(userProgress = {}) {
    const availableModules = this.modules.filter(module => !module.isLocked);
    if (availableModules.length === 0) return 0;
    const completedCount = this.getCompletedModulesCount(userProgress);
    return (completedCount / availableModules.length) * 100;
  }

  isCompleted(userProgress = {}) {
    const availableModules = this.modules.filter(module => !module.isLocked);
    if (availableModules.length === 0) return true; // If no available modules, consider completed
    return availableModules.every(module =>
      userProgress[module.id]?.completed
    );
  }

  // Calculate total quiz questions from available modules
  getTotalQuizQuestions() {
    return this.modules
      .filter(module => !module.isComingSoon)
      .reduce((total, module) => total + (module.totalQuizQuestions || 0), 0);
  }

  // Calculate total steps from available modules
  getTotalSteps() {
    return this.modules
      .filter(module => !module.isComingSoon)
      .reduce((total, module) => total + (module.totalSteps || 0), 0);
  }
}